# 本次任务对齐说明（E4/E5 跑通 + 真实对话重放实验）

本文聚焦于将已确定的选型固化到实验说明，并对 E4/E5 的跑通与新增“真实对话重放（Replay）”实验给出统一的工程计划与产出标准，便于我们两边对齐理解与推进。

## 一、已确认的选型（冻结）
- 分割：采用“语义分割”为默认；朴素分割仅作回退/基线。
- 检索：默认优选 Manticore；pgvector 作为回退与对照。
- 抽取：采用 `langextract` 为主路径；规则化摘要为回退。

本次已将上述结论补充到以下实验说明中：
- experiments/E1_retrieval_engine/README.md
- experiments/E2_text_splitting/README.md
- experiments/E3_extraction/README.md

## 二、E4/E5 跑通计划

目标：在当前仓库的“可本地运行（最小依赖）”脚本基础上，完成一次端到端演示与结果产出，确保后续与 Replay 实验联动。

- E4（编排对比）
  - 运行：`python experiments/E4_orchestration/run_experiment.py`
  - 成功标准：
    - 生成 `experiments/E4_orchestration/results/state_transitions.json`
    - 生成 `experiments/E4_orchestration/results/metrics.csv`
    - `results/notes.md` 更新一次本地与“LangGraph Lite（模拟）”的时延/步骤对比
  - 关注点：状态转换记录完整、异常分支可观测（错误转移、重试）

- E5（事件一致性）
  - 运行：`python experiments/E5_events_consistency/run_experiment.py`
  - 成功标准：
    - 生成 `experiments/E5_events_consistency/results/metrics.csv`
    - 覆盖 Streams/Outbox 两类路径的成功率/乱序率/延迟
  - 关注点：结果字段与仓库汇总脚本口径一致，便于后续对比

注：上面两项运行为“本地最小路径”，真正接入外部中间件（Kafka/Redis、DB）在后续真实栈联调阶段再启用。

## 三、新增实验：真实对话重放（Replay）

目标：对 `experiments/real_dialogues/` 的真实参考数据按 PRD v1.1 的典型用户流程进行重放；逐轮记录检索片段、拼接上下文与“双边摘要”，用于验证“可回溯复盘”和端到端检索拼装策略。

- 目录与产物（已建 README）
  - 根目录：`experiments/real_dialogues_replay/README.md`
  - 产物：
    - `results/sessions/<session_id>/round_*.json`（逐轮明细）
    - `results/replay_report.md`（逐轮可读报告）

- 执行策略（模型与组件）
  - 分割：默认“语义分割”，参数（窗口 800–1200、重叠 15%–20%、标题继承）
  - 检索：默认 Manticore；环境变量 `SEARCH_ENGINE=manticore|pgvector`
  - 抽取：`langextract` 输出双边摘要与 anchors；`EXTRACTOR=langextract`
- 模型：
    - 基础重放（成本优先、真实在线）：`MODEL=gpt-5-nano`
    - 技术跑通后真实对比：`MODEL=gemini-2.5-pro`
    - 说明：LLM 通过聚合提供商（OpenAI 兼容）调用，仅需切换 `MODEL`，无需切换 Provider。

- 逐轮记录 Schema（核心字段）
  - `turn_id, user_text`
  - `retrieved_chunks[]`：`{doc_id, chunk_id, offsets, score, title}`
  - `stitched_ctx_tokens`：拼接后上下文 token 预算与组成比例（短期/长期锚点/文档）
  - `answer_preview`：生成结果前 200–300 字符
  - `summary`：`{user, assistant, intent, key_points[], anchors[]}`（anchors 可回到 message_id/chunk_id）

- 运行（占位命令，待脚本落地）
  - 基础重放：
    `MODEL=gpt-5-nano SEARCH_ENGINE=manticore SPLIT=semantic python experiments/real_dialogues_replay/run_replay.py --corpus_dir experiments/real_dialogues`
  - 真实对比：
    `MODEL=gemini-2.5-pro SEARCH_ENGINE=manticore SPLIT=semantic python experiments/real_dialogues_replay/run_replay.py --corpus_dir experiments/real_dialogues --compare_saved`

## 四、验收标准与评估指标

- 可回溯性：摘要 anchors 点击能定位到原文（chunk/offset 或回合 message_id）
- 检索质量：R@K / nDCG / Top1 覆盖率；跨标题污染率
- 成本与延迟：上下文 token 预算，检索/生成分段时延
- 产物完整性：E4/E5/Replay 结果文件齐备、格式与汇总脚本兼容

## 五、风险与缓解

- 外部模型/网络不可达：基础重放使用低成本真实模型 `gpt-5-nano`；仍不可达时仅做本地仿真以打通流水线，不纳入对比结论。
- 引擎/向量不一致：统一通过 `SEARCH_ENGINE` 与切分参数配置，产出时记录元数据便于回溯。
- anchors 偏差：在 E3 侧收集“锚点失配率”并留出回退（规则化摘要）。

## 六、下一步

- [ ] 跑通 E4/E5 本地最小路径并提交结果文件
- [ ] 实现 `real_dialogues_replay/run_replay.py` 的最小版本（先写入 round_* JSON，再补充报告）
- [ ] 与 `gemini-2.5-pro` 的真实对比跑通后，更新 Replay 报告，产出对照样例

如对以上对齐说明没有异议，我会按此计划推进实现与验证。
