[{"orchestrator": "local_state_machine", "conversation_id": "conv_1_local", "from_state": "init", "to_state": "processing", "duration_ms": 15.011072158813477, "trigger": "user_input_received", "metadata": {"input_length": 8}}, {"orchestrator": "local_state_machine", "conversation_id": "conv_1_local", "from_state": "processing", "to_state": "waiting_retrieval", "duration_ms": 60.040950775146484, "trigger": "start_retrieval", "metadata": {"input_length": 8}}, {"orchestrator": "local_state_machine", "conversation_id": "conv_1_local", "from_state": "waiting_retrieval", "to_state": "generating_response", "duration_ms": 110.05973815917969, "trigger": "retrieval_completed", "metadata": {"input_length": 8}}, {"orchestrator": "local_state_machine", "conversation_id": "conv_1_local", "from_state": "generating_response", "to_state": "completed", "duration_ms": 7.5321197509765625, "trigger": "response_generated", "metadata": {"input_length": 8}}, {"orchestrator": "langgraph_lite", "conversation_id": "conv_1_langgraph", "from_state": "init", "to_state": "processing", "duration_ms": 27.042150497436523, "trigger": "node_input_processor", "metadata": {"node": "input_processor", "graph_overhead": true}}, {"orchestrator": "langgraph_lite", "conversation_id": "conv_1_langgraph", "from_state": "processing", "to_state": "waiting_retrieval", "duration_ms": 74.54371452331543, "trigger": "node_retrieval_node", "metadata": {"node": "retrieval_node", "graph_overhead": true}}, {"orchestrator": "langgraph_lite", "conversation_id": "conv_1_langgraph", "from_state": "waiting_retrieval", "to_state": "generating_response", "duration_ms": 154.06513214111328, "trigger": "node_generation_node", "metadata": {"node": "generation_node", "graph_overhead": true}}, {"orchestrator": "langgraph_lite", "conversation_id": "conv_1_langgraph", "from_state": "generating_response", "to_state": "completed", "duration_ms": 14.418840408325195, "trigger": "node_output_formatter", "metadata": {"node": "output_formatter", "graph_overhead": true}}, {"orchestrator": "langgraph_lite", "conversation_id": "conv_1_langgraph", "from_state": "completed", "to_state": "error", "duration_ms": 0, "trigger": "error", "metadata": {"error": "No module named 'psutil'"}}, {"orchestrator": "local_state_machine", "conversation_id": "conv_2_local", "from_state": "init", "to_state": "processing", "duration_ms": 15.02084732055664, "trigger": "user_input_received", "metadata": {"input_length": 31}}, {"orchestrator": "local_state_machine", "conversation_id": "conv_2_local", "from_state": "processing", "to_state": "waiting_retrieval", "duration_ms": 60.029029846191406, "trigger": "start_retrieval", "metadata": {"input_length": 31}}, {"orchestrator": "local_state_machine", "conversation_id": "conv_2_local", "from_state": "waiting_retrieval", "to_state": "generating_response", "duration_ms": 110.04090309143066, "trigger": "retrieval_completed", "metadata": {"input_length": 31}}, {"orchestrator": "local_state_machine", "conversation_id": "conv_2_local", "from_state": "generating_response", "to_state": "completed", "duration_ms": 7.526874542236328, "trigger": "response_generated", "metadata": {"input_length": 31}}, {"orchestrator": "langgraph_lite", "conversation_id": "conv_2_langgraph", "from_state": "init", "to_state": "processing", "duration_ms": 27.013063430786133, "trigger": "node_input_processor", "metadata": {"node": "input_processor", "graph_overhead": true}}, {"orchestrator": "langgraph_lite", "conversation_id": "conv_2_langgraph", "from_state": "processing", "to_state": "waiting_retrieval", "duration_ms": 82.01909065246582, "trigger": "node_retrieval_node", "metadata": {"node": "retrieval_node", "graph_overhead": true}}, {"orchestrator": "langgraph_lite", "conversation_id": "conv_2_langgraph", "from_state": "waiting_retrieval", "to_state": "generating_response", "duration_ms": 154.03199195861816, "trigger": "node_generation_node", "metadata": {"node": "generation_node", "graph_overhead": true}}, {"orchestrator": "langgraph_lite", "conversation_id": "conv_2_langgraph", "from_state": "generating_response", "to_state": "completed", "duration_ms": 14.417886734008789, "trigger": "node_output_formatter", "metadata": {"node": "output_formatter", "graph_overhead": true}}, {"orchestrator": "langgraph_lite", "conversation_id": "conv_2_langgraph", "from_state": "completed", "to_state": "error", "duration_ms": 0, "trigger": "error", "metadata": {"error": "No module named 'psutil'"}}, {"orchestrator": "local_state_machine", "conversation_id": "conv_3_local", "from_state": "init", "to_state": "processing", "duration_ms": 15.017032623291016, "trigger": "user_input_received", "metadata": {"input_length": 12}}, {"orchestrator": "local_state_machine", "conversation_id": "conv_3_local", "from_state": "processing", "to_state": "waiting_retrieval", "duration_ms": 60.062408447265625, "trigger": "start_retrieval", "metadata": {"input_length": 12}}, {"orchestrator": "local_state_machine", "conversation_id": "conv_3_local", "from_state": "waiting_retrieval", "to_state": "generating_response", "duration_ms": 109.69066619873047, "trigger": "retrieval_completed", "metadata": {"input_length": 12}}, {"orchestrator": "local_state_machine", "conversation_id": "conv_3_local", "from_state": "generating_response", "to_state": "completed", "duration_ms": 7.554054260253906, "trigger": "response_generated", "metadata": {"input_length": 12}}, {"orchestrator": "langgraph_lite", "conversation_id": "conv_3_langgraph", "from_state": "init", "to_state": "processing", "duration_ms": 23.797035217285156, "trigger": "node_input_processor", "metadata": {"node": "input_processor", "graph_overhead": true}}, {"orchestrator": "langgraph_lite", "conversation_id": "conv_3_langgraph", "from_state": "processing", "to_state": "waiting_retrieval", "duration_ms": 76.12490653991699, "trigger": "node_retrieval_node", "metadata": {"node": "retrieval_node", "graph_overhead": true}}, {"orchestrator": "langgraph_lite", "conversation_id": "conv_3_langgraph", "from_state": "waiting_retrieval", "to_state": "generating_response", "duration_ms": 154.04295921325684, "trigger": "node_generation_node", "metadata": {"node": "generation_node", "graph_overhead": true}}, {"orchestrator": "langgraph_lite", "conversation_id": "conv_3_langgraph", "from_state": "generating_response", "to_state": "completed", "duration_ms": 14.431953430175781, "trigger": "node_output_formatter", "metadata": {"node": "output_formatter", "graph_overhead": true}}, {"orchestrator": "langgraph_lite", "conversation_id": "conv_3_langgraph", "from_state": "completed", "to_state": "error", "duration_ms": 0, "trigger": "error", "metadata": {"error": "No module named 'psutil'"}}, {"orchestrator": "local_state_machine", "conversation_id": "conv_4_local", "from_state": "init", "to_state": "processing", "duration_ms": 15.031099319458008, "trigger": "user_input_received", "metadata": {"input_length": 32}}, {"orchestrator": "local_state_machine", "conversation_id": "conv_4_local", "from_state": "processing", "to_state": "waiting_retrieval", "duration_ms": 60.03999710083008, "trigger": "start_retrieval", "metadata": {"input_length": 32}}, {"orchestrator": "local_state_machine", "conversation_id": "conv_4_local", "from_state": "waiting_retrieval", "to_state": "generating_response", "duration_ms": 110.0759506225586, "trigger": "retrieval_completed", "metadata": {"input_length": 32}}, {"orchestrator": "local_state_machine", "conversation_id": "conv_4_local", "from_state": "generating_response", "to_state": "completed", "duration_ms": 7.543087005615234, "trigger": "response_generated", "metadata": {"input_length": 32}}, {"orchestrator": "langgraph_lite", "conversation_id": "conv_4_langgraph", "from_state": "init", "to_state": "processing", "duration_ms": 27.013778686523438, "trigger": "node_input_processor", "metadata": {"node": "input_processor", "graph_overhead": true}}, {"orchestrator": "langgraph_lite", "conversation_id": "conv_4_langgraph", "from_state": "processing", "to_state": "waiting_retrieval", "duration_ms": 82.02004432678223, "trigger": "node_retrieval_node", "metadata": {"node": "retrieval_node", "graph_overhead": true}}, {"orchestrator": "langgraph_lite", "conversation_id": "conv_4_langgraph", "from_state": "waiting_retrieval", "to_state": "generating_response", "duration_ms": 154.0360450744629, "trigger": "node_generation_node", "metadata": {"node": "generation_node", "graph_overhead": true}}, {"orchestrator": "langgraph_lite", "conversation_id": "conv_4_langgraph", "from_state": "generating_response", "to_state": "completed", "duration_ms": 14.444112777709961, "trigger": "node_output_formatter", "metadata": {"node": "output_formatter", "graph_overhead": true}}, {"orchestrator": "langgraph_lite", "conversation_id": "conv_4_langgraph", "from_state": "completed", "to_state": "error", "duration_ms": 0, "trigger": "error", "metadata": {"error": "No module named 'psutil'"}}, {"orchestrator": "local_state_machine", "conversation_id": "conv_5_local", "from_state": "init", "to_state": "processing", "duration_ms": 13.33475112915039, "trigger": "user_input_received", "metadata": {"input_length": 10}}, {"orchestrator": "local_state_machine", "conversation_id": "conv_5_local", "from_state": "processing", "to_state": "waiting_retrieval", "duration_ms": 58.82000923156738, "trigger": "start_retrieval", "metadata": {"input_length": 10}}, {"orchestrator": "local_state_machine", "conversation_id": "conv_5_local", "from_state": "waiting_retrieval", "to_state": "generating_response", "duration_ms": 109.05289649963379, "trigger": "retrieval_completed", "metadata": {"input_length": 10}}, {"orchestrator": "local_state_machine", "conversation_id": "conv_5_local", "from_state": "generating_response", "to_state": "completed", "duration_ms": 7.534980773925781, "trigger": "response_generated", "metadata": {"input_length": 10}}, {"orchestrator": "langgraph_lite", "conversation_id": "conv_5_langgraph", "from_state": "init", "to_state": "processing", "duration_ms": 27.031898498535156, "trigger": "node_input_processor", "metadata": {"node": "input_processor", "graph_overhead": true}}, {"orchestrator": "langgraph_lite", "conversation_id": "conv_5_langgraph", "from_state": "processing", "to_state": "waiting_retrieval", "duration_ms": 82.03792572021484, "trigger": "node_retrieval_node", "metadata": {"node": "retrieval_node", "graph_overhead": true}}, {"orchestrator": "langgraph_lite", "conversation_id": "conv_5_langgraph", "from_state": "waiting_retrieval", "to_state": "generating_response", "duration_ms": 150.20012855529785, "trigger": "node_generation_node", "metadata": {"node": "generation_node", "graph_overhead": true}}, {"orchestrator": "langgraph_lite", "conversation_id": "conv_5_langgraph", "from_state": "generating_response", "to_state": "completed", "duration_ms": 14.411687850952148, "trigger": "node_output_formatter", "metadata": {"node": "output_formatter", "graph_overhead": true}}, {"orchestrator": "langgraph_lite", "conversation_id": "conv_5_langgraph", "from_state": "completed", "to_state": "error", "duration_ms": 0, "trigger": "error", "metadata": {"error": "No module named 'psutil'"}}]