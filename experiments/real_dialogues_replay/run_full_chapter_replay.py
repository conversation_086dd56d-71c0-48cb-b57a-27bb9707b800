#!/usr/bin/env python3
"""
整章直供（Full Chapter Replay）实验脚本

目标：验证“每轮都发送：知深导师人设 + 整章原文 + 动态摘要 + 最近N轮”的上下文拼装，
不做任何检索或分块召回的尝试，确保体验基线可用。

说明：
- 交互式回放：支持从命令行输入问题，多轮对话；
- 每轮：构建上下文 -> 调用 LLM（若不可用，返回占位文本）-> 生成/更新“历史摘要” -> 保存调试信息；
- 与 run_optimized_replay 的差异：这里“整章直供”为强制策略，无文档长度开关；
- Persona：读取 experiments/real_dialogues/persona-prompt.md 中的“知深学习导师”提示；
- 语料：默认从 experiments/real_dialogues/ 中选取包含“解读”的示例文档（可按需更换）。
"""

import os
import re
import json
import time
import glob
import argparse
from typing import List, Dict, Any

from dotenv import load_dotenv

# util 抽取：摘要器与上下文拼接（知深导师）
from experiments.utils.context_builder import build_full_chapter_context

ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
DEFAULT_CORPUS_DIR = os.path.join(ROOT, 'real_dialogues')
RESULTS_DIR = os.path.join(ROOT, 'real_dialogues_replay', 'results')

os.makedirs(RESULTS_DIR, exist_ok=True)
load_dotenv()
load_dotenv(os.path.join(ROOT, '.env'))


# ------------------------- 基础工具 -------------------------

def read_file(path: str) -> str:
    with open(path, 'r', encoding='utf-8') as f:
        return f.read()


def estimate_tokens(text: str) -> int:
    """粗略估算 token 数量（中文按字符数，英文按单词数）"""
    chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
    english_words = len(re.findall(r'[a-zA-Z]+', text))
    return chinese_chars + english_words


def call_llm(prompt: str, model: str | None = None) -> str:
    """调用 OpenAI 兼容聚合接口；失败时返回占位文本。"""
    try:
        import openai
        client = openai.OpenAI(
            api_key=os.getenv('OPENAI_API_KEY'),
            base_url=os.getenv('OPENAI_BASE_URL')
        )
        model = model or os.getenv('MODEL', 'gpt-5-nano')
        resp = client.chat.completions.create(
            model=model,
            messages=[{"role": "user", "content": prompt}],
            temperature=0.3,
        )
        return resp.choices[0].message.content
    except Exception as e:
        return f"[mock-answer due to LLM error: {e}]"


def generate_conversation_summary(conversation_history: List[Dict[str, str]]) -> str:
    # 保留兼容接口；内部已迁移到 utils/context_builder
    from experiments.utils.context_builder import generate_conversation_summary as _g
    return _g(conversation_history)


def save_round_files(session_dir: str, round_num: int, debug_info: Dict[str, Any]):
    os.makedirs(session_dir, exist_ok=True)
    debug_path = os.path.join(session_dir, f"round_{round_num:03d}_debug.json")
    with open(debug_path, 'w', encoding='utf-8') as f:
        json.dump(debug_info, f, ensure_ascii=False, indent=2)

    readable_path = os.path.join(session_dir, f"round_{round_num:03d}_readable.md")
    with open(readable_path, 'w', encoding='utf-8') as f:
        f.write(f"# Round {round_num} 调试信息\n\n")
        f.write(f"## 用户输入\n{debug_info['user_input']}\n\n")
        f.write("## 上下文策略\n")
        f.write(f"- 策略: {debug_info['context']['strategy']}\n")
        f.write(f"- 上下文长度: {debug_info['context']['total_length']} 字符\n\n")
        f.write("## 上下文（截断预览）\n")
        preview = debug_info['context']['full_context'][:1200].replace("\n", "\n\n")
        f.write(preview + ("\n\n...\n" if len(debug_info['context']['full_context']) > 1200 else "\n"))
        f.write(f"\n## AI 回答\n{debug_info['ai_response']}\n")


# ------------------------- 主流程 -------------------------

def run(args):
    # 读取源文档（默认选择含“解读”的样例文档；如无则选任意 .md）
    doc_candidates = glob.glob(os.path.join(args.corpus_dir, "*解读*.md"))
    if not doc_candidates:
        doc_candidates = glob.glob(os.path.join(args.corpus_dir, "*.md"))
    if not doc_candidates:
        raise FileNotFoundError("未在语料目录下找到可用文档 (.md)")
    source_doc_path = doc_candidates[0]
    source_text = read_file(source_doc_path)

    # Persona（知深导师）
    persona_path = os.path.join(args.corpus_dir, 'persona-prompt.md')
    persona_text = read_file(persona_path) if os.path.exists(persona_path) else ''

    # 运行目录
    ts = time.strftime('%Y%m%d-%H%M%S')
    run_name = f"full-chapter-{os.getenv('MODEL', 'gpt-5-nano')}-{ts}"
    run_dir = os.path.join(RESULTS_DIR, 'runs', run_name)
    os.makedirs(run_dir, exist_ok=True)
    session_id = f"full_chapter_session_{ts}"
    session_dir = os.path.join(run_dir, 'sessions', session_id)
    os.makedirs(session_dir, exist_ok=True)

    # 对话循环
    conversation_history: List[Dict[str, str]] = []
    current_user_input = args.initial_question or input("请输入第一个问题: ").strip()

    round_num = 1
    while round_num <= args.max_rounds:
        print("\n" + "=" * 50)
        print(f"Round {round_num}")
        print("=" * 50)
        print(f"用户: {current_user_input}")

        start_ts = time.time()
        context = build_full_chapter_context(
            document_text=source_text,
            persona_text=persona_text,
            conversation_history=conversation_history,
            max_recent_turns=args.max_history,
        )

        # 构造 Prompt
        prompt = "\n\n".join([
            context['full_context'],
            f"[当前问题]\n{current_user_input}",
            "要求：你是知深学习导师。基于提供的整章原文与既往对话，用简洁中文回答；\n"
            "- 优先结合原文概念阐释；如根据常识补充，请明确说明；\n"
            "- 每次聚焦1-2个关键点，以问题结尾引导下一步；\n"
            "- 若用户困惑，给出更简单类比；\n"
            "- 不要复述整章；仅引用必要部分，不要大段抄写。"
        ])

        ai_response = call_llm(prompt)

        debug_info = {
            'round': round_num,
            'user_input': current_user_input,
            'context': context,
            'ai_response': ai_response,
            'timing_ms': int((time.time() - start_ts) * 1000),
            'doc_path': source_doc_path,
        }
        save_round_files(session_dir, round_num, debug_info)

        # 更新历史
        conversation_history.append({"role": "user", "content": current_user_input})
        conversation_history.append({"role": "assistant", "content": ai_response})

        # 下一轮
        if round_num < args.max_rounds:
            nxt = input("\n请输入下一轮问题（或输入 'quit' 退出）: ").strip()
            if nxt.lower() == 'quit':
                print("用户选择退出。")
                break
            current_user_input = nxt
        round_num += 1

    # 简要报告
    report_path = os.path.join(run_dir, 'replay_report.md')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(f"# Full Chapter Replay 报告\n\n")
        f.write(f"- 文档: {os.path.basename(source_doc_path)}\n")
        f.write(f"- Persona: {'有' if persona_text else '无'}\n")
        f.write(f"- 会话目录: {session_dir}\n")
        f.write(f"- 轮数: {round_num - 1}\n")
    print(f"\n✓ 完成。会话目录: {session_dir}\n报告: {report_path}")


def build_arg_parser() -> argparse.ArgumentParser:
    p = argparse.ArgumentParser(description='整章直供（Full Chapter）重放')
    p.add_argument('--corpus_dir', default=DEFAULT_CORPUS_DIR, help='语料目录')
    p.add_argument('--max_rounds', type=int, default=5, help='最多对话轮数')
    p.add_argument('--max_history', type=int, default=2, help='保留的最近对话轮数（用于原文保留，摘要之外）')
    p.add_argument('--initial_question', type=str, default=None, help='初始问题（可选）')
    return p


if __name__ == '__main__':
    parser = build_arg_parser()
    args = parser.parse_args()
    run(args)
