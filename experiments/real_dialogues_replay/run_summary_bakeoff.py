#!/usr/bin/env python3
"""
按轮对真实对话生成摘要的对比实验（LLM vs langextract）。

输入：experiments/real_dialogues/*_chatlog.md（或指定 corpus_dir）
输出：per-round 两路摘要 JSON、简单指标（耗时、长度近似），便于比较质量/性能/成本。

说明：
- 网络/凭证不可用时，LLM/langextract 会回退到最小摘要，保证流程可跑通。
- 不做检索，仅针对对话摘要本身做 bakeoff。
"""

import os
import re
import json
import time
import glob
import argparse
from typing import List, Dict, Any

from dotenv import load_dotenv

from experiments.utils.summarizer import summarize_structured

ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
DEFAULT_CORPUS_DIR = os.path.join(ROOT, 'real_dialogues')
RESULTS_DIR = os.path.join(ROOT, 'real_dialogues_replay', 'results')

os.makedirs(RESULTS_DIR, exist_ok=True)
load_dotenv()
load_dotenv(os.path.join(ROOT, '.env'))


def read_file(path: str) -> str:
    with open(path, 'r', encoding='utf-8') as f:
        return f.read()


def extract_turns(md_text: str) -> List[Dict[str, Any]]:
    blocks = re.split(r"\n---\n", md_text)
    turns: List[Dict[str, Any]] = []
    turn_id = 0
    cur_user = None
    for b in blocks:
        if '## 🧑‍💻 User' in b:
            content = b.split('## 🧑‍💻 User', 1)[-1]
            content = re.sub(r'^#+.*\n', '', content).strip()
            turn_id += 1
            cur_user = {"turn_id": turn_id, "user": content}
        elif '## 🤖 Assistant' in b:
            content = b.split('## 🤖 Assistant', 1)[-1]
            content = re.sub(r'^#+.*\n', '', content).strip()
            if cur_user:
                turns.append({"turn_id": cur_user['turn_id'], "user": cur_user['user'], "assistant": content})
                cur_user = None
    return turns


def run(args):
    # 找 chatlog 文件
    dlg_candidates = glob.glob(os.path.join(args.corpus_dir, "*_chatlog.md"))
    if not dlg_candidates:
        # 回退：任意包含“chatlog”的 md
        dlg_candidates = glob.glob(os.path.join(args.corpus_dir, "*chatlog*.md"))
    if not dlg_candidates:
        raise FileNotFoundError("未找到 *_chatlog.md 文件")
    dialogue_path = dlg_candidates[0]
    text = read_file(dialogue_path)
    turns = extract_turns(text)
    if not turns:
        raise RuntimeError("未解析到任何对话轮次")

    ts = time.strftime('%Y%m%d-%H%M%S')
    run_name = f"summary-bakeoff-{ts}"
    run_dir = os.path.join(RESULTS_DIR, 'runs', run_name)
    os.makedirs(run_dir, exist_ok=True)
    session_dir = os.path.join(run_dir, 'sessions', os.path.splitext(os.path.basename(dialogue_path))[0])
    os.makedirs(session_dir, exist_ok=True)

    report_lines = [
        f"# 摘要对比实验（LLM vs langextract）",
        f"- chatlog: {os.path.basename(dialogue_path)}",
        f"- 轮数: {len(turns)}",
        ""
    ]

    rows: List[Dict[str, Any]] = []

    for t in turns:
        u = t['user']
        a = t.get('assistant', '')

        # LLM 路径
        t0 = time.time()
        s_llm = summarize_structured(u, a, method='llm', anchors=[])
        t1 = time.time()

        # langextract 路径
        t2 = time.time()
        s_lx = summarize_structured(u, a, method='langextract', anchors=[])
        t3 = time.time()

        out = {
            'turn_id': t['turn_id'],
            'user_text_len': len(u),
            'assistant_text_len': len(a),
            'llm': s_llm,
            'langextract': s_lx,
            'timing_ms': {
                'llm': int((t1 - t0) * 1000),
                'langextract': int((t3 - t2) * 1000),
            }
        }

        # 保存每轮
        with open(os.path.join(session_dir, f"round_{t['turn_id']:03d}.json"), 'w', encoding='utf-8') as f:
            json.dump(out, f, ensure_ascii=False, indent=2)

        # 报告预览
        report_lines.append(f"## Round {t['turn_id']}")
        report_lines.append(f"- LLM intent: {s_llm.get('intent')} | key_points: {len(s_llm.get('key_points', []))} | {out['timing_ms']['llm']}ms")
        report_lines.append(f"- LX  intent: {s_lx.get('intent')} | key_points: {len(s_lx.get('key_points', []))} | {out['timing_ms']['langextract']}ms")
        report_lines.append("")

        rows.append(out)

    # 汇总报告
    report_path = os.path.join(run_dir, 'replay_report.md')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("\n".join(report_lines))

    # 简单 CSV（长度与耗时）
    csv_path = os.path.join(run_dir, 'summary_metrics.csv')
    with open(csv_path, 'w', encoding='utf-8') as f:
        f.write('turn_id,user_len,assistant_len,llm_ms,langextract_ms,llm_kp,lx_kp\n')
        for r in rows:
            f.write(
                f"{r['turn_id']},{r['user_text_len']},{r['assistant_text_len']},{r['timing_ms']['llm']},{r['timing_ms']['langextract']},{len(r['llm'].get('key_points', []))},{len(r['langextract'].get('key_points', []))}\n"
            )

    print(f"完成。会话目录: {session_dir}\n报告: {report_path}\n指标: {csv_path}")


def build_arg_parser():
    import argparse
    p = argparse.ArgumentParser(description='真实对话摘要对比实验（LLM vs langextract）')
    p.add_argument('--corpus_dir', default=DEFAULT_CORPUS_DIR, help='语料目录（包含 *_chatlog.md）')
    return p


if __name__ == '__main__':
    parser = build_arg_parser()
    args = parser.parse_args()
    run(args)

