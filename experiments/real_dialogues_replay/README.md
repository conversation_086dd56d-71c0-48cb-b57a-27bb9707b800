# 实验：真实对话重放（Replay）与检索拼装可观测性

目标：基于 `experiments/real_dialogues/` 的真实资料，按 PRD v1.1 的“典型用户流程”重放端到端对话，逐轮记录：
- 检索到的原文片段（`doc_id/chunk_id/offsets`）
- 拼接后的上下文（短期记忆 + 语义锚点 + 文档 TopK）
- 每一轮生成的“双边摘要”（`speaker/intent/key_points/anchors[]`）

已对齐的选型：
- 分割：默认“语义分割”（朴素仅作回退/对照）
- 检索：默认 Manticore（pgvector 作为回退）
- 抽取：默认 `langextract`（规则法作回退）

模型使用策略：
- 基础重放（成本优先）：使用真实在线模型 `gpt-5-nano` 作为低成本模型进行真实生成与摘要；
- 实际对比（风格对齐）：技术链路跑通后，切换到 `gemini-2.5-pro` 进行同样的重放，对比对话风格与引用效果。
 说明：LLM 提供商为聚合服务，通过 `.env` 指定统一的 API（OpenAI 兼容）；仅需切换 `MODEL` 即可，无需切换 Provider。

目录结构
```
experiments/real_dialogues_replay/
  README.md
  run_replay.py            # 重放主脚本（后续补充）
  config.yaml              # TopK/窗口/引擎/模型等参数（后续补充）
  results/
    runs/
      <RUN_NAME>/
        sessions/
          <session_id>/
            round_001.json     # 每轮：检索片段/上下文/答案预览/摘要/anchors
            round_002.json
        replay_report.md       # 汇总报告（逐轮可读对照）
  run_full_chapter_replay.py   # 新增：整章直供模式（不做检索，强制全章+摘要+最近N轮）
  run_optimized_replay.py      # 优化回放（短文档直送，长文档提示检索占位）
  run_summary_bakeoff.py       # 新增：真实对话逐轮摘要对比（LLM vs langextract）
```

运行说明
- 基础重放（低成本真实生成）：
  `MODEL=gpt-5-nano EMBED_MODEL=text-embedding-3-small SEARCH_ENGINE=manticore python experiments/real_dialogues_replay/run_replay.py --max_rounds 6 --max_history 2`
- Gemini 对比（独立 run 目录）：
  `MODEL=gemini-2.5-pro EMBED_MODEL=text-embedding-3-small SEARCH_ENGINE=manticore python experiments/real_dialogues_replay/run_replay.py --max_rounds 6 --max_history 2`
  或自定义名称避免混淆：`--run_name gemini-pro-replay-01`

- 整章直供（当前验证重点，强制全章直送）：
  `MODEL=gpt-5-nano python experiments/real_dialogues_replay/run_full_chapter_replay.py --max_rounds 6 --max_history 2`
  说明：每轮发送 Persona（知深导师）+ 历史摘要 + 最近N轮 + 整章原文，不做任何检索或切片召回。

- 摘要对比（真实 chatlog，逐轮生成）：
  `python experiments/real_dialogues_replay/run_summary_bakeoff.py`
  输出每轮 LLM 与 langextract 两路结构化摘要、简单指标（时延、要点数）。

环境变量策略（成本/质量分工）
- INTENT_MODEL（默认 `gpt-5-nano`）：意图识别、摘要等“后台任务”；
- MODEL（例如 `gemini-2.5-pro`）：用户可见回答；
- LX_MODEL（例如 `gemini-2.5-flash`）：langextract 抽取模型；
- EXTRACTOR：设置为 `langextract` 时启用 langextract；否则回退为小模型 JSON 摘要。

输出与指标
- 逐轮字段：`turn_id, user_text, retrieved_chunks[], stitched_ctx_tokens, answer_preview, summary{user,assistant,anchors[]}`
- 指标：检索覆盖率、跨标题污染率、上下文预算、anchors 命中率、端到端延迟（可选）

整章直供脚本的输出
- `round_XXX_debug.json`：包含上下文拼装详情（strategy=full_chapter、历史摘要、最近N轮、full_context长度等）与 AI 回答；
- `round_XXX_readable.md`：可读版预览（上下文前1200字截断 + 回答），便于人工评估“导师式”连贯性；
- `replay_report.md`：本次会话的简要信息汇总。

备注
- 重放脚本复用现有能力：检索（E1 Provider 抽象）、切分（E2 语义分割默认）、摘要/anchors（E3 `langextract`）。
- 基线/回退：`MODEL=gpt-5-nano` 为基础重放默认；网络/凭证不可用时允许本地仿真回退，仅用于流水线打通，不计入对比结论。
 - 本次验证重点为“整章直供 + 动态摘要”：短期内不做语义分块检索，先确保导师体验完整、连贯。
