"""
Context builder utilities for "知深导师".

Provides:
- build_full_chapter_context: Persona + historical summary + recent N turns + full chapter text
- generate_conversation_summary: LLM-driven, compact, third-person summary for earlier turns

Design goals:
- Keep API minimal and pure; caller controls IO and persistence.
- Be tolerant to missing network (fall back to simple concatenation when LLM unavailable).
"""

from __future__ import annotations

import os
import re
from typing import List, Dict, Any

from dotenv import load_dotenv

load_dotenv()


def _estimate_tokens(text: str) -> int:
    chinese_chars = len(re.findall(r"[\u4e00-\u9fff]", text))
    english_words = len(re.findall(r"[a-zA-Z]+", text))
    return chinese_chars + english_words


def _call_llm(prompt: str, model: str | None = None) -> str:
    try:
        import openai
        client = openai.OpenAI(
            api_key=os.getenv('OPENAI_API_KEY'),
            base_url=os.getenv('OPENAI_BASE_URL'),
        )
        model = model or os.getenv('INTENT_MODEL', os.getenv('MODEL', 'gpt-5-nano'))
        resp = client.chat.completions.create(
            model=model,
            messages=[{"role": "user", "content": prompt}],
            temperature=0,
        )
        return resp.choices[0].message.content
    except Exception as e:
        return f"[mock-summary due to LLM error: {e}]"


def generate_conversation_summary(conversation_history: List[Dict[str, str]], *, max_chars: int = 220) -> str:
    """Generate a compact summary for earlier dialogue turns.

    - Keep the last N turns outside this function; caller decides truncation.
    - Return Chinese third-person narrative within `max_chars` when possible.
    """
    if not conversation_history:
        return ""
    raw = "\n".join([f"{h['role']}: {h['content']}" for h in conversation_history])
    prompt = f"""请将以下对话历史总结为简洁摘要（不超过{max_chars}字），用于帮助AI快速恢复上下文：

{raw}

要求：
1) 用第三人称中文；2) 提炼用户的主要问题/困惑与AI的关键解释；3) 不引入新信息；4) 输出一段话即可。
摘要："""
    return "[历史摘要] " + _call_llm(prompt)


def build_full_chapter_context(*,
                               document_text: str,
                               persona_text: str,
                               conversation_history: List[Dict[str, str]],
                               max_recent_turns: int = 2,
                               summary_chars: int = 220) -> Dict[str, Any]:
    """Build full-chapter context with persona, historical summary, recent turns, and the chapter text.

    Returns dict with: strategy, conversation_summary, recent_conversation, full_context, total_length.
    """
    parts: List[str] = []

    if persona_text.strip():
        parts.append(f"[Persona]\n{persona_text.strip()}")

    recent = conversation_history[-(max_recent_turns * 2):] if conversation_history else []
    early = conversation_history[:-(max_recent_turns * 2)] if conversation_history and len(conversation_history) > (max_recent_turns * 2) else []

    conversation_summary = ""
    if early:
        conversation_summary = generate_conversation_summary(early, max_chars=summary_chars)
        if conversation_summary:
            parts.append(conversation_summary)

    if recent:
        parts.append("[最近对话]\n" + "\n".join([f"{h['role']}: {h['content']}" for h in recent]))

    parts.append(f"[文档内容-整章]\n{document_text}")

    full_context = "\n\n".join(parts)
    return {
        'strategy': 'full_chapter',
        'conversation_summary': conversation_summary,
        'recent_conversation': "\n".join([f"{h['role']}: {h['content']}" for h in recent]) if recent else "",
        'full_context': full_context,
        'total_length': len(full_context),
    }

