"""
Summarization utilities for real-dialogue experiments.

Provides two methods for per-round summaries:
- LLM (JSON): user_summary, assistant_summary, intent (1-3 chars), key_points[]
- langextract: map extractions to the same schema when available

Design:
- Robust to missing network/keys; returns fallback minimal summaries.
- Consistent schema for downstream comparison.
"""

from __future__ import annotations

import os
import json
import time
from typing import Dict, Any, List, Optional

from dotenv import load_dotenv

load_dotenv()

try:
    import langextract as lx  # type: ignore
    LANGEXTRACT_AVAILABLE = True
except Exception:
    LANGEXTRACT_AVAILABLE = False


def _call_llm_json(system: str, user: str, *, model: Optional[str] = None) -> Optional[Dict[str, Any]]:
    try:
        import openai
        client = openai.OpenAI(
            api_key=os.getenv('OPENAI_API_KEY'),
            base_url=os.getenv('OPENAI_BASE_URL'),
        )
        model = model or os.getenv('INTENT_MODEL', os.getenv('MODEL', 'gpt-5-nano'))
        resp = client.chat.completions.create(
            model=model,
            messages=[{"role": "system", "content": system}, {"role": "user", "content": user}],
            temperature=0,
        )
        txt = resp.choices[0].message.content
        return json.loads(txt)
    except Exception:
        return None


def summarize_structured(user_text: str,
                         assistant_text: str,
                         *,
                         method: str = 'llm',
                         anchors: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
    """Return a structured summary dict with fields:
    { user_summary, assistant_summary, intent, key_points[], anchors[] }
    method: 'llm' | 'langextract'
    """
    anchors = anchors or []

    if method == 'langextract' and LANGEXTRACT_AVAILABLE:
        try:
            text = f"用户: {user_text}\nAI: {assistant_text}"
            prompt_description = (
                "从这一次对话回合中提取：\n"
                "- dialogue_turn: speaker(user/ai)、intent(中文1-3字)、topic(可选)\n"
                "- key_concept: 对话中的关键信息点（短语）\n"
                "请确保 extraction_text 为原文片段，注意中文输出。"
            )
            model_id = os.getenv('LX_MODEL', 'gemini-2.5-flash')
            doc = lx.extract(text_or_documents=text, prompt_description=prompt_description, model_id=model_id)
            intent = 'general'
            key_points: List[str] = []
            if doc and getattr(doc, 'extractions', None):
                for ext in doc.extractions:
                    cls = getattr(ext, 'extraction_class', '')
                    attrs = getattr(ext, 'attributes', {}) or {}
                    if cls == 'dialogue_turn' and isinstance(attrs, dict):
                        sp = attrs.get('speaker')
                        if sp == 'user' and attrs.get('intent'):
                            intent = str(attrs['intent'])
                    elif cls == 'key_concept':
                        if getattr(ext, 'extraction_text', None):
                            key_points.append(str(ext.extraction_text))
                        elif isinstance(attrs, dict) and attrs.get('concept'):
                            key_points.append(str(attrs['concept']))
            return {
                'user_summary': user_text[:200],
                'assistant_summary': assistant_text[:200],
                'intent': intent or 'general',
                'key_points': key_points[:8],
                'anchors': anchors,
                'method': 'langextract'
            }
        except Exception:
            # fall through to LLM or fallback
            pass

    if method == 'llm':
        system = (
            "你是一个对话信息提取器。只输出合法 JSON，不要其它任何文字。\n"
            "目标：将冗长对话转换为信息密度高的简化记录，保留所有有价值信息。\n"
            "字段说明:\n"
            "- user_summary: 用户发言的完整信息提取，包含所有问题、观点、需求，去除废话但保留信息量\n"
            "- assistant_summary: AI回应的核心内容提取，包含解释、建议、引导，保持逻辑完整\n"
            "- intent: 用户意图分类(中文1-3字)\n"
            "- key_points: 本轮对话的关键信息点数组\n"
            "- anchors: 留空数组"
        )
        user = (
            f"用户原话: {user_text}\n助手回答: {assistant_text}\n\n"
            "请提取对话信息，要求:\n"
            "1. user_summary: 完整提取用户表达的所有有效信息，去除口语化表达但保留完整意思\n"
            "2. assistant_summary: 提取AI回应的核心内容和逻辑，保持信息完整性\n"
            "3. intent: 入门/释疑/练习/复盘/规划/澄清/讨论等\n"
            "4. key_points: 本轮对话产生的具体信息点\n"
            "目标：让人看摘要就能理解原对话的完整信息内容。"
        )
        data = _call_llm_json(system, user)
        if isinstance(data, dict):
            data['anchors'] = anchors
            data['method'] = 'llm'
            # normalize names if needed
            if 'user' in data and 'user_summary' not in data:
                data['user_summary'] = data.pop('user')
            if 'assistant' in data and 'assistant_summary' not in data:
                data['assistant_summary'] = data.pop('assistant')
            for k in ('user_summary', 'assistant_summary', 'intent', 'key_points', 'anchors'):
                data.setdefault(k, '' if k in ('user_summary', 'assistant_summary', 'intent') else [] )
            return data

    # Fallback minimal
    return {
        'user_summary': user_text[:160],
        'assistant_summary': assistant_text[:160],
        'intent': 'general',
        'key_points': [],
        'anchors': anchors,
        'method': method if method in ('llm', 'langextract') else 'fallback'
    }


