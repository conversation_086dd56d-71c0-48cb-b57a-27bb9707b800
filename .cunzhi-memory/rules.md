# 开发规范和规则

- Manticore Search 正确使用方法：
1. 启动服务：cd experiments && docker compose up -d
2. 检查状态：docker compose ps（确保 exp_manticore 为 healthy）
3. 测试连接：curl -s http://localhost:9308/sql -X POST -H "Content-Type: application/json" -d '{"query": "SELECT 1"}'
4. Python 客户端使用：
   - 导入：import manticoresearch
   - 配置：configuration = manticoresearch.Configuration(host="http://localhost:9308")
   - 创建表：utils_api.sql("CREATE TABLE name (field type, embedding float_vector knn_type='hnsw' knn_dims='384' hnsw_similarity='cosine')")
   - 插入数据：index_api.insert({'table': 'name', 'doc': {'field': 'value', 'embedding': [0.1, 0.2, ...]}})
   - KNN 搜索：search_api.search({'table': 'name', 'knn': {'field': 'embedding', 'query_vector': [0.1, 0.2, ...], 'k': 5}})
5. 注意事项：
   - 向量维度必须与模型输出维度匹配
   - 每次重建表时需要先 DROP TABLE IF EXISTS
   - 搜索结果在 response.hits 中，通过 hit._source 访问字段
